#!/usr/bin/env python3
"""
Interactive User Input Loop for Layout Bricks Instructions Analyzer

This script provides an interactive loop where the user can:
1. Run analysis on different folders
2. Check API call status
3. View analysis results
4. Exit the program

Usage:
    python userinput.py
"""

import os
import sys
from pathlib import Path

# Add the current directory to the Python path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import check_api_calls_remaining, DEFAULT_INPUT_FOLDER, DEFAULT_OUTPUT_FOLDER
from analyzer import LayoutBricksAnalyzer
from config import get_api_key


def display_menu():
    """Display the main menu options."""
    print("\n" + "="*60)
    print("LAYOUT BRICKS INSTRUCTIONS ANALYZER - INTERACTIVE MODE")
    print("="*60)
    print("1. Run analysis on input folder")
    print("2. Run analysis on custom folder")
    print("3. Check remaining API calls")
    print("4. View recent analysis results")
    print("5. Exit")
    print("-"*60)


def get_user_choice():
    """Get and validate user choice."""
    while True:
        try:
            choice = input("Enter your choice (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                return int(choice)
            else:
                print("Invalid choice. Please enter a number between 1 and 5.")
        except KeyboardInterrupt:
            print("\nExiting...")
            return 5
        except Exception as e:
            print(f"Error reading input: {e}")


def run_analysis(input_folder=None, output_folder=None):
    """Run analysis on the specified folder."""
    try:
        # Use defaults if not specified
        if not input_folder:
            input_folder = DEFAULT_INPUT_FOLDER
        if not output_folder:
            output_folder = DEFAULT_OUTPUT_FOLDER
            
        print(f"\nRunning analysis on: {input_folder}")
        print(f"Output folder: {output_folder}")
        
        # Get API key
        api_key = get_api_key()
        
        # Initialize analyzer
        analyzer = LayoutBricksAnalyzer(api_key=api_key)
        
        # Run analysis
        results = analyzer.analyze_folder(input_folder, output_folder)
        
        # Display results summary
        if not results:
            print("\nNo new files were processed in this run.")
            print("All files may have been processed already.")
        else:
            print(f"\n{'='*40}")
            print("ANALYSIS RESULTS SUMMARY")
            print(f"{'='*40}")
            
            for result in results:
                print(f"File: {Path(result['file_path']).name}")
                print(f"Score: {result['score']}/10")
                if 'confidence' in result:
                    print(f"Confidence: {result['confidence']}")
                if result.get('error'):
                    print(f"Error: {result['error']}")
                print("-" * 30)
            
            # Statistics
            scores = [r['score'] for r in results if not r.get('error')]
            if scores:
                avg_score = sum(scores) / len(scores)
                high_scores = len([s for s in scores if s >= 7])
                print(f"\nStatistics:")
                print(f"Files analyzed: {len(results)}")
                print(f"Average score: {avg_score:.2f}")
                print(f"High scores (7+): {high_scores}")
        
        print(f"\nResults saved to: {output_folder}")
        
    except Exception as e:
        print(f"Error during analysis: {e}")


def check_api_status():
    """Check and display API call status."""
    try:
        print("\nChecking API call status...")
        result = check_api_calls_remaining()
        
        if result['success']:
            print(f"Remaining API calls: {result['remaining_calls']}")
            if result.get('data'):
                # Display additional info if available
                data = result['data']
                if 'usage' in data:
                    usage = data['usage']
                    print(f"Usage details: {usage}")
        else:
            print(f"Failed to check API calls: {result['error']}")
            
    except Exception as e:
        print(f"Error checking API status: {e}")


def view_recent_results():
    """View recent analysis results."""
    try:
        output_path = Path(DEFAULT_OUTPUT_FOLDER)
        csv_file = output_path / "analysis_results.csv"
        json_file = output_path / "analysis_summary.json"
        
        print(f"\nLooking for results in: {output_path}")
        
        if csv_file.exists():
            print(f"CSV file found: {csv_file}")
            print(f"File size: {csv_file.stat().st_size} bytes")
        else:
            print("No CSV results file found.")
            
        if json_file.exists():
            print(f"JSON file found: {json_file}")
            print(f"File size: {json_file.stat().st_size} bytes")
        else:
            print("No JSON results file found.")
            
        # Count analysis files
        analysis_files = list(output_path.glob("*_analysis.txt"))
        print(f"Individual analysis files: {len(analysis_files)}")
        
    except Exception as e:
        print(f"Error viewing results: {e}")


def main():
    """Main interactive loop."""
    print("Starting Interactive Layout Bricks Instructions Analyzer...")
    
    while True:
        display_menu()
        choice = get_user_choice()
        
        if choice == 1:
            # Run analysis on default input folder
            run_analysis()
            
        elif choice == 2:
            # Run analysis on custom folder
            input_folder = input(f"Enter input folder path (default: {DEFAULT_INPUT_FOLDER}): ").strip()
            if not input_folder:
                input_folder = DEFAULT_INPUT_FOLDER
                
            output_folder = input(f"Enter output folder path (default: {DEFAULT_OUTPUT_FOLDER}): ").strip()
            if not output_folder:
                output_folder = DEFAULT_OUTPUT_FOLDER
                
            run_analysis(input_folder, output_folder)
            
        elif choice == 3:
            # Check API status
            check_api_status()
            
        elif choice == 4:
            # View recent results
            view_recent_results()
            
        elif choice == 5:
            # Exit
            print("\nExiting the program. Goodbye!")
            break
            
        # Wait for user to continue
        input("\nPress Enter to continue...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user. Exiting...")
        sys.exit(0)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
