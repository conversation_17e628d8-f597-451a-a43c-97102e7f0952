"""
File utility functions for the Layout Bricks Instructions Analyzer.

This module provides functions for file discovery, processing tracking,
and detailed analysis output generation.
"""

import os
import csv
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set

logger = logging.getLogger(__name__)


def find_txt_files(input_folder: Path) -> List[Path]:
    """Find all .txt files in the folder and subfolders."""
    txt_files = []
    for root, dirs, files in os.walk(input_folder):
        for file in files:
            if file.lower().endswith('.txt'):
                txt_files.append(Path(root) / file)
    return txt_files


def load_processed_files(output_path: Path) -> Set[str]:
    """Load list of already processed files from CSV if it exists."""
    processed_files = set()
    csv_path = output_path / "analysis_results.csv"
    if csv_path.exists():
        try:
            with open(csv_path, 'r', encoding='utf-8', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    processed_files.add(row['file_path'])
            logger.info(f"Loaded {len(processed_files)} already processed files")
        except Exception as e:
            logger.warning(f"Could not load processed files list: {e}")
    return processed_files


def save_reasoning_file(analysis_result: Dict, output_folder: Path):
    """Save detailed reasoning to a separate file."""
    file_path = Path(analysis_result["file_path"])

    # Create a safe filename based on the original file path
    safe_filename = str(file_path).replace('\\', '_').replace('/', '_').replace(':', '_')
    reasoning_filename = f"{safe_filename}_analysis.txt"
    reasoning_path = output_folder / reasoning_filename

    # Ensure output directory exists
    reasoning_path.parent.mkdir(parents=True, exist_ok=True)

    # Write reasoning file
    with open(reasoning_path, 'w', encoding='utf-8') as f:
        f.write(f"Layout Bricks Instructions Analysis\n")
        f.write(f"{'=' * 50}\n\n")
        f.write(f"Original File: {analysis_result['file_path']}\n")
        f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Score: {analysis_result['score']}/10\n\n")

        # Handle relevant_segments (can be list or string)
        relevant_segments = analysis_result.get('relevant_segments', '')
        if isinstance(relevant_segments, list):
            relevant_segments_str = '; '.join(relevant_segments)
        else:
            relevant_segments_str = str(relevant_segments) if relevant_segments else ''

        f.write(f"Relevant Segments: {relevant_segments_str}\n\n")
        f.write(f"Detailed Explanation:\n")
        f.write(f"{'-' * 20}\n")
        f.write(f"{analysis_result.get('explanation', '')}\n")

        if analysis_result.get('error'):
            f.write(f"\nError: {analysis_result['error']}\n")
